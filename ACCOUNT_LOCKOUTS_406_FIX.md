# 🔧 Account Lockouts 406 Error Fix

## Problem
You're experiencing a **406 (Not Acceptable)** error when querying the `account_lockouts` table:
```
GET https://rclikclltlyzyojjttqv.supabase.co/rest/v1/account_lockouts?select=*&email=eq.geffkmoney%40gmail.com&locked_until=gte.2025-06-26T11%3A11%3A56.946Z 406 (Not Acceptable)
```

## Root Cause Analysis
The 406 error typically occurs due to:

1. **Missing Table** - The `account_lockouts` table doesn't exist in your database
2. **RLS Policy Issues** - Row Level Security policies are blocking access
3. **Missing Permissions** - The table lacks proper permissions for the `authenticated` or `anon` roles
4. **Header Issues** - Incorrect Accept headers (already fixed in your Supabase client)

## ✅ Solution Steps

### Step 1: Run the Database Fix Script
1. **Open your Supabase Dashboard**: https://supabase.com/dashboard/project/rclikclltlyzyojjttqv/sql
2. **Go to SQL Editor**
3. **Copy and paste the entire contents** of `database/fix_account_lockouts_406.sql`
4. **Click "Run"** to execute the script

This script will:
- ✅ Create the `account_lockouts` table if it doesn't exist
- ✅ Create the `login_attempts` table (dependency)
- ✅ Set up proper indexes for performance
- ✅ Enable Row Level Security with permissive policies
- ✅ Grant necessary permissions
- ✅ Test the table functionality
- ✅ Verify the setup

### Step 2: Test the Fix
1. **Open the test file**: `test-account-lockouts-fix.html` in your browser
2. **Run all 4 tests** to verify the fix works correctly
3. **Check that all tests pass** ✅

### Step 3: Verify in Your Application
The following code should now work without 406 errors:

```typescript
// Check if account is locked
const { data, error } = await supabase
  .from('account_lockouts')
  .select('*')
  .eq('email', '<EMAIL>')
  .gte('locked_until', new Date().toISOString());

if (error) {
  console.error('Error:', error); // Should be null now
} else {
  console.log('Lockout data:', data); // Should return array
}
```

## 🔍 What the Fix Does

### Database Changes
1. **Creates Tables**: Ensures both `account_lockouts` and `login_attempts` tables exist
2. **Sets Up Indexes**: Optimizes query performance
3. **Configures RLS**: Enables Row Level Security with permissive policies
4. **Grants Permissions**: Allows both `authenticated` and `anon` roles to access the tables

### Table Structure
```sql
CREATE TABLE account_lockouts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL,
    locked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    locked_until TIMESTAMP WITH TIME ZONE NOT NULL,
    attempt_count INTEGER NOT NULL DEFAULT 0,
    reason TEXT NOT NULL
);
```

### RLS Policies
```sql
-- Permissive policy allowing all operations
CREATE POLICY "Allow all operations on account_lockouts" 
ON account_lockouts FOR ALL USING (true);
```

## 🚨 Security Note
The current RLS policy is permissive (`USING (true)`) for demo purposes. In production, you should implement more restrictive policies:

```sql
-- Example: More secure policy
DROP POLICY "Allow all operations on account_lockouts" ON account_lockouts;

-- Allow users to check their own lockout status
CREATE POLICY "Users can check own lockout status" ON account_lockouts
    FOR SELECT USING (email = auth.jwt() ->> 'email');

-- Allow system to manage lockouts (requires service role)
CREATE POLICY "System can manage lockouts" ON account_lockouts
    FOR ALL USING (auth.role() = 'service_role');
```

## 🧪 Testing
After running the fix, test these scenarios:

1. **Basic Query**: `SELECT * FROM account_lockouts LIMIT 5`
2. **Email Filter**: `SELECT * FROM account_lockouts WHERE email = '<EMAIL>'`
3. **Date Filter**: `SELECT * FROM account_lockouts WHERE locked_until >= NOW()`
4. **Combined Filter**: The original failing query

## 📝 Files Created/Modified
- ✅ `database/fix_account_lockouts_406.sql` - Main fix script
- ✅ `test-account-lockouts-fix.html` - Test verification tool
- ✅ `create_export_tables.sql` - Updated with DROP POLICY statements
- ✅ `ACCOUNT_LOCKOUTS_406_FIX.md` - This documentation

## 🎯 Expected Results
After applying the fix:
- ❌ 406 errors should be eliminated
- ✅ Account lockout queries should work correctly
- ✅ Your authentication flow should function properly
- ✅ The `accountSecurity.isAccountLocked()` method should work

## 🔄 Next Steps
1. Run the SQL fix script
2. Test with the HTML test tool
3. Verify in your application
4. Consider implementing more secure RLS policies for production
5. Monitor for any remaining issues

If you continue to experience issues after running this fix, the problem may be related to:
- Network connectivity
- Supabase service status
- Browser cache (try hard refresh)
- Different table name or schema
