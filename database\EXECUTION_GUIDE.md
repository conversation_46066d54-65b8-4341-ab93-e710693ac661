# Database Setup Execution Guide

## Problem
The system is stuck on loading when running large SQL scripts, likely due to:
- Missing dependencies (user_profiles table)
- Large script size causing timeouts
- Foreign key constraint errors

## Solution: Step-by-Step Execution

### Step 1: Diagnose Current State
Run this first to see what's currently in your database:
```sql
-- Copy and paste contents of: database/diagnose_database.sql
```

### Step 2: Create Foundation Tables
Run this to ensure user_profiles table exists with correct structure:
```sql
-- Copy and paste contents of: database/step_by_step_fix.sql
```

### Step 3: Create Export Tables (Part 1)
Run basic tables first:
```sql
-- Copy and paste contents of: database/export_tables_part1.sql
```

### Step 4: Create Export Tables (Part 2)
Run security tables:
```sql
-- Copy and paste contents of: database/export_tables_part2.sql
```

### Step 5: Create Export Tables (Part 3)
Run functions and triggers:
```sql
-- Copy and paste contents of: database/export_tables_part3.sql
```

## How to Execute

1. **Open Supabase SQL Editor**
   - Go to your Supabase project dashboard
   - Click "SQL Editor" in the left sidebar

2. **Run Each Script Separately**
   - Copy the contents of each file
   - Paste into a new query in SQL Editor
   - Click "Run" and wait for completion
   - Check for any errors before proceeding to next step

3. **Verify Success**
   After each step, run this to check tables were created:
   ```sql
   SELECT table_name FROM information_schema.tables 
   WHERE table_schema = 'public' 
   ORDER BY table_name;
   ```

## Expected Tables After Completion
- user_profiles
- keyword_searches  
- user_sessions
- account_security
- security_audit_log

## Troubleshooting

### If you get "relation does not exist" errors:
- Make sure you ran step_by_step_fix.sql first
- Check that user_profiles table was created successfully

### If you get timeout errors:
- The scripts are now broken into smaller chunks
- Run each part separately with a few seconds between each

### If you get permission errors:
- Make sure you're using the service role key in Supabase
- Or run with admin privileges

## Next Steps
After successful execution, your application should be able to:
- Create user profiles
- Track user sessions
- Log security events
- Export data properly
