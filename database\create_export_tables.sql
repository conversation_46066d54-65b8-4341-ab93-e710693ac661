-- Export Tables Creation Script
-- This script creates tables for exporting data from the scraping and analysis system
-- Run AFTER ensuring user_profiles table exists (run step_by_step_fix.sql first)

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Keyword Searches Table
-- Stores search history for keyword research functionality
CREATE TABLE IF NOT EXISTS keyword_searches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    search_term TEXT NOT NULL,
    result_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_result_count CHECK (result_count >= 0)
);

-- Export Jobs Table
-- Tracks export operations and their status
CREATE TABLE IF NOT EXISTS export_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    export_type VARCHAR(50) NOT NULL CHECK (export_type IN ('csv', 'json', 'xlsx', 'pdf')),
    table_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    file_path TEXT,
    file_size BIGINT,
    row_count INTEGER DEFAULT 0,
    filters JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_by UUID, -- Could reference users table if implemented
    
    -- Constraints
    CONSTRAINT valid_file_size CHECK (file_size >= 0),
    CONSTRAINT valid_row_count CHECK (row_count >= 0)
);

-- Export Data Table
-- Stores metadata about exported files
CREATE TABLE IF NOT EXISTS export_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    export_job_id UUID NOT NULL REFERENCES export_jobs(id) ON DELETE CASCADE,
    original_table VARCHAR(100) NOT NULL,
    exported_rows INTEGER DEFAULT 0,
    file_format VARCHAR(10) NOT NULL,
    compression_used BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    last_downloaded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_exported_rows CHECK (exported_rows >= 0),
    CONSTRAINT valid_download_count CHECK (download_count >= 0)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_keyword_searches_search_term ON keyword_searches(search_term);
CREATE INDEX IF NOT EXISTS idx_keyword_searches_created_at ON keyword_searches(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_export_jobs_status ON export_jobs(status);
CREATE INDEX IF NOT EXISTS idx_export_jobs_created_at ON export_jobs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_export_data_export_job_id ON export_data(export_job_id);

-- Add comments for documentation
COMMENT ON TABLE keyword_searches IS 'Stores search history for keyword research functionality';
COMMENT ON TABLE export_jobs IS 'Tracks export operations and their status';
COMMENT ON TABLE export_data IS 'Stores metadata about exported files';

COMMENT ON COLUMN keyword_searches.search_term IS 'The keyword term that was searched';
COMMENT ON COLUMN keyword_searches.result_count IS 'Number of results returned for the search';
COMMENT ON COLUMN export_jobs.export_type IS 'Format of the export (csv, json, xlsx, pdf)';
COMMENT ON COLUMN export_jobs.table_name IS 'Name of the source table being exported';
COMMENT ON COLUMN export_jobs.status IS 'Current status of the export job';
