-- Export Tables Part 2: Security Tables
-- Run this after export_tables_part1.sql

-- Account Security Table
CREATE TABLE IF NOT EXISTS account_security (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    password_hash TEXT,
    salt TEXT,
    failed_login_attempts INTEGER DEFAULT 0,
    account_locked_until TIMESTAMP WITH TIME ZONE,
    password_reset_token TEXT,
    password_reset_expires TIMESTAMP WITH TIME ZONE,
    email_verification_token TEXT,
    email_verified BOOLEAN DEFAULT FALSE,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret TEXT,
    backup_codes TEXT[],
    security_questions JSONB,
    last_password_change TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_failed_attempts CHECK (failed_login_attempts >= 0),
    CONS<PERSON>AINT valid_password_reset CHECK (
        (password_reset_token IS NULL AND password_reset_expires IS NULL) OR
        (password_reset_token IS NOT NULL AND password_reset_expires IS NOT NULL)
    ),
    UNIQUE(user_id)
);

-- Create indexes for account_security
CREATE INDEX IF NOT EXISTS idx_account_security_user_id ON account_security(user_id);
CREATE INDEX IF NOT EXISTS idx_account_security_reset_token ON account_security(password_reset_token);
CREATE INDEX IF NOT EXISTS idx_account_security_email_token ON account_security(email_verification_token);
CREATE INDEX IF NOT EXISTS idx_account_security_locked ON account_security(account_locked_until);

-- Enable RLS
ALTER TABLE account_security ENABLE ROW LEVEL SECURITY;

-- RLS Policies for account_security
DROP POLICY IF EXISTS "Users can view own security settings" ON account_security;
DROP POLICY IF EXISTS "Users can update own security settings" ON account_security;
DROP POLICY IF EXISTS "Users can insert own security settings" ON account_security;

CREATE POLICY "Users can view own security settings" ON account_security
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own security settings" ON account_security
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own security settings" ON account_security
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Security Audit Log Table
CREATE TABLE IF NOT EXISTS security_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    event_type TEXT NOT NULL,
    event_description TEXT,
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN DEFAULT TRUE,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_event_type CHECK (event_type IN (
        'login_attempt', 'login_success', 'login_failure',
        'logout', 'password_change', 'password_reset_request',
        'password_reset_success', 'email_verification',
        'two_factor_enabled', 'two_factor_disabled',
        'account_locked', 'account_unlocked',
        'suspicious_activity', 'data_export', 'data_deletion'
    ))
);

-- Create indexes for security_audit_log
CREATE INDEX IF NOT EXISTS idx_security_audit_user_id ON security_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_security_audit_event_type ON security_audit_log(event_type);
CREATE INDEX IF NOT EXISTS idx_security_audit_created ON security_audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_security_audit_success ON security_audit_log(success);

-- Enable RLS
ALTER TABLE security_audit_log ENABLE ROW LEVEL SECURITY;

-- RLS Policies for security_audit_log
DROP POLICY IF EXISTS "Users can view own audit logs" ON security_audit_log;
DROP POLICY IF EXISTS "Allow insert for audit logs" ON security_audit_log;

CREATE POLICY "Users can view own audit logs" ON security_audit_log
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Allow insert for audit logs" ON security_audit_log
    FOR INSERT WITH CHECK (true);
