-- Export Tables Part 3: Triggers and Functions
-- Run this after export_tables_part2.sql

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function to clean expired sessions
CREATE OR REPLACE FUNCTION clean_expired_sessions()
RETURNS void AS $$
BEGIN
    DELETE FROM user_sessions 
    WHERE expires_at < NOW() OR (last_activity < NOW() - INTERVAL '30 days');
END;
$$ language 'plpgsql';

-- Function to log security events
CREATE OR REPLACE FUNCTION log_security_event(
    p_user_id UUID,
    p_event_type TEXT,
    p_event_description TEXT DEFAULT NULL,
    p_ip_address INET DEFAULT NULL,
    p_user_agent TEXT DEFAULT NULL,
    p_success BOOLEAN DEFAULT TRUE,
    p_metadata JSONB DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO security_audit_log (
        user_id, event_type, event_description, 
        ip_address, user_agent, success, metadata
    ) VALUES (
        p_user_id, p_event_type, p_event_description,
        p_ip_address, p_user_agent, p_success, p_metadata
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ language 'plpgsql';

-- Function to check account security status
CREATE OR REPLACE FUNCTION check_account_security(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
    security_info JSONB;
    failed_attempts INTEGER;
    is_locked BOOLEAN;
    two_factor_enabled BOOLEAN;
    email_verified BOOLEAN;
BEGIN
    SELECT 
        failed_login_attempts,
        (account_locked_until IS NOT NULL AND account_locked_until > NOW()),
        COALESCE(two_factor_enabled, FALSE),
        COALESCE(email_verified, FALSE)
    INTO failed_attempts, is_locked, two_factor_enabled, email_verified
    FROM account_security 
    WHERE user_id = p_user_id;
    
    security_info := jsonb_build_object(
        'failed_attempts', COALESCE(failed_attempts, 0),
        'is_locked', COALESCE(is_locked, FALSE),
        'two_factor_enabled', two_factor_enabled,
        'email_verified', email_verified,
        'security_score', CASE 
            WHEN two_factor_enabled AND email_verified THEN 'high'
            WHEN email_verified THEN 'medium'
            ELSE 'low'
        END
    );
    
    RETURN security_info;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
DROP TRIGGER IF EXISTS update_account_security_updated_at ON account_security;
DROP TRIGGER IF EXISTS update_user_sessions_activity ON user_sessions;

CREATE TRIGGER update_account_security_updated_at
    BEFORE UPDATE ON account_security
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_sessions_activity
    BEFORE UPDATE ON user_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create a view for user security summary
CREATE OR REPLACE VIEW user_security_summary AS
SELECT 
    up.id,
    up.email,
    up.role,
    up.created_at as user_created_at,
    acs.email_verified,
    acs.two_factor_enabled,
    acs.failed_login_attempts,
    acs.account_locked_until,
    acs.last_password_change,
    (SELECT COUNT(*) FROM user_sessions WHERE user_id = up.id AND is_active = TRUE) as active_sessions,
    (SELECT COUNT(*) FROM security_audit_log WHERE user_id = up.id AND created_at > NOW() - INTERVAL '30 days') as recent_security_events
FROM user_profiles up
LEFT JOIN account_security acs ON up.id = acs.user_id;
