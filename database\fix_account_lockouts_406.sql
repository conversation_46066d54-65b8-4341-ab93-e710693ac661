-- Fix 406 Error for account_lockouts table
-- Run this script in your Supabase SQL Editor to resolve the 406 error

-- Step 1: Check if the table exists
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_schema = 'public' AND table_name = 'account_lockouts') THEN
        RAISE NOTICE 'account_lockouts table does not exist. Creating it now...';
    ELSE
        RAISE NOTICE 'account_lockouts table already exists.';
    END IF;
END $$;

-- Step 2: Create the account_lockouts table if it doesn't exist
CREATE TABLE IF NOT EXISTS account_lockouts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL,
    locked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    locked_until TIMESTAMP WITH TIME ZONE NOT NULL,
    attempt_count INTEGER NOT NULL DEFAULT 0,
    reason TEXT NOT NULL
);

-- Step 3: Create the login_attempts table if it doesn't exist (dependency)
CREATE TABLE IF NOT EXISTS login_attempts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL,
    success BOOLEAN NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    attempted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    failure_reason TEXT
);

-- Step 4: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_login_attempts_email ON login_attempts(email);
CREATE INDEX IF NOT EXISTS idx_login_attempts_attempted_at ON login_attempts(attempted_at);
CREATE INDEX IF NOT EXISTS idx_login_attempts_success ON login_attempts(success);
CREATE INDEX IF NOT EXISTS idx_account_lockouts_email ON account_lockouts(email);
CREATE INDEX IF NOT EXISTS idx_account_lockouts_locked_until ON account_lockouts(locked_until);

-- Step 5: Enable RLS on security tables
ALTER TABLE login_attempts ENABLE ROW LEVEL SECURITY;
ALTER TABLE account_lockouts ENABLE ROW LEVEL SECURITY;

-- Step 6: Drop existing policies if they exist
DROP POLICY IF EXISTS "Allow all operations on login_attempts" ON login_attempts;
DROP POLICY IF EXISTS "Allow all operations on account_lockouts" ON account_lockouts;

-- Step 7: Create permissive RLS policies (allow all operations for now)
CREATE POLICY "Allow all operations on login_attempts" ON login_attempts FOR ALL USING (true);
CREATE POLICY "Allow all operations on account_lockouts" ON account_lockouts FOR ALL USING (true);

-- Step 8: Grant necessary permissions
GRANT ALL ON account_lockouts TO authenticated;
GRANT ALL ON login_attempts TO authenticated;
GRANT ALL ON account_lockouts TO anon;
GRANT ALL ON login_attempts TO anon;

-- Step 9: Test the table by inserting and querying a test record
DO $$
DECLARE
    test_email TEXT := '<EMAIL>';
    test_lockout_id UUID;
BEGIN
    -- Insert a test lockout record
    INSERT INTO account_lockouts (email, locked_until, attempt_count, reason)
    VALUES (test_email, NOW() + INTERVAL '1 hour', 3, 'Test lockout for 406 error fix')
    RETURNING id INTO test_lockout_id;
    
    RAISE NOTICE 'Test lockout record created with ID: %', test_lockout_id;
    
    -- Try to query it back
    IF EXISTS (SELECT 1 FROM account_lockouts WHERE email = test_email) THEN
        RAISE NOTICE 'Test query successful - account_lockouts table is working correctly';
    ELSE
        RAISE NOTICE 'Test query failed - there may still be an issue';
    END IF;
    
    -- Clean up test record
    DELETE FROM account_lockouts WHERE email = test_email;
    RAISE NOTICE 'Test record cleaned up';
END $$;

-- Step 10: Verify table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'account_lockouts'
ORDER BY ordinal_position;

-- Step 11: Verify RLS policies
SELECT 
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies 
WHERE tablename = 'account_lockouts' 
    AND schemaname = 'public';

-- Success message
SELECT 'account_lockouts table setup completed successfully!' as status;
