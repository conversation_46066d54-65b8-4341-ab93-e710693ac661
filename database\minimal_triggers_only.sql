-- Just create the triggers without the complex functions
-- This is the absolute minimum from Part 3

-- Simple function for updating timestamps (already exists from earlier)
-- CREATE OR REPLACE FUNCTION update_updated_at_column()
-- RETURNS TRIGGER AS $$
-- BEGIN
--     NEW.updated_at = NOW();
--     RETURN NEW;
-- END;
-- $$ language 'plpgsql';

-- Just create the triggers (function should already exist)
DROP TRIGGER IF EXISTS update_account_security_updated_at ON account_security;
DROP TRIGGER IF EXISTS update_user_sessions_activity ON user_sessions;

CREATE TRIGGER update_account_security_updated_at 
    BEFORE UPDATE ON account_security 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_sessions_activity 
    BEFORE UPDATE ON user_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
