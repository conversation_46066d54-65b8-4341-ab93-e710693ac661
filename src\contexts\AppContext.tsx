import React, { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { supabase } from '@/lib/supabase';

interface User {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  subscription_plan: 'free' | 'pro' | 'enterprise';
  subscription_status: 'active' | 'cancelled' | 'past_due';
}

interface AppSettings {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    marketing: boolean;
  };
  privacy: {
    analytics: boolean;
    data_collection: boolean;
  };
}

interface AppStats {
  total_scrapes: number;
  total_exports: number;
  total_leads: number;
  total_keywords: number;
  api_requests_used: number;
  api_requests_limit: number;
  storage_used: number; // in MB
  storage_limit: number; // in MB
}

interface RecentActivity {
  id: string;
  type: 'scrape' | 'export' | 'lead' | 'keyword' | 'analysis';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'error' | 'pending';
}

interface AppContextType {
  // UI State
  sidebarOpen: boolean;
  toggleSidebar: () => void;
  currentTool: string;
  setCurrentTool: (tool: string) => void;

  // User & Auth
  user: User | null;
  setUser: (user: User | null) => void;
  isAuthenticated: boolean;

  // App Data
  scrapingData: any[];
  setScrapingData: (data: any[]) => void;

  // Settings
  settings: AppSettings;
  updateSettings: (settings: Partial<AppSettings>) => void;

  // Statistics
  stats: AppStats;
  refreshStats: () => Promise<void>;

  // Recent Activity
  recentActivity: RecentActivity[];
  addActivity: (activity: Omit<RecentActivity, 'id' | 'timestamp'>) => void;

  // Global Loading States
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;

  // Global Error State
  globalError: string | null;
  setGlobalError: (error: string | null) => void;

  // Notifications
  notifications: any[];
  addNotification: (notification: any) => void;
  removeNotification: (id: string) => void;
  markNotificationAsRead: (id: string) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useAppContext must be used within an AppProvider');
  }
  return context;
};

interface AppProviderProps {
  children: ReactNode;
}

export const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  // UI State
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [currentTool, setCurrentTool] = useState('scraper');
  const [isLoading, setIsLoading] = useState(false);
  const [globalError, setGlobalError] = useState<string | null>(null);

  // User & Auth
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // App Data
  const [scrapingData, setScrapingData] = useState([]);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);

  // Settings
  const [settings, setSettings] = useState<AppSettings>({
    theme: 'system',
    language: 'en',
    timezone: 'America/New_York',
    notifications: {
      email: true,
      push: false,
      marketing: true
    },
    privacy: {
      analytics: true,
      data_collection: true
    }
  });

  // Statistics
  const [stats, setStats] = useState<AppStats>({
    total_scrapes: 0,
    total_exports: 0,
    total_leads: 0,
    total_keywords: 0,
    api_requests_used: 0,
    api_requests_limit: 10000,
    storage_used: 0,
    storage_limit: 1000
  });

  // Load initial data
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Check if user is already authenticated
        const { data: { user } } = await supabase.auth.getUser();
        if (user) {
          setUser({
            id: user.id,
            email: user.email || '',
            name: user.user_metadata?.name || '',
            avatar_url: user.user_metadata?.avatar_url,
            subscription_plan: 'free',
            subscription_status: 'active'
          });
          setIsAuthenticated(true);
        }

        // Load other data (notifications, activity) that don't require auth
        loadRecentActivity();
        loadNotifications();
      } catch (error) {
        console.error('Failed to initialize app:', error);
      }
    };

    initializeApp();
  }, []);

  // Auth state listener
  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (session?.user) {
        setUser({
          id: session.user.id,
          email: session.user.email || '',
          name: session.user.user_metadata?.name || '',
          avatar_url: session.user.user_metadata?.avatar_url,
          subscription_plan: 'free',
          subscription_status: 'active'
        });
        setIsAuthenticated(true);

        // Load settings after authentication - inline to avoid dependency issues
        try {
          const { data, error } = await supabase
            .from('user_settings')
            .select('*')
            .eq('user_id', session.user.id)
            .single();

          if (error && error.code !== 'PGRST116') {
            console.error('Failed to load settings:', error);
          } else if (data) {
            setSettings(prev => ({ ...prev, ...data }));
          }
        } catch (error) {
          console.error('Failed to load settings:', error);
        }

        // Load stats after authentication - inline to avoid dependency issues
        try {
          // Mock stats for now since we don't have the actual implementation
          setStats({
            totalScrapes: 0,
            successfulScrapes: 0,
            failedScrapes: 0,
            dataPointsCollected: 0
          });
        } catch (error) {
          console.error('Failed to load stats:', error);
        }
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []); // Empty dependency array to avoid circular dependencies

  const loadUserData = useCallback(async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUser({
          id: user.id,
          email: user.email || '',
          name: user.user_metadata?.name || '',
          avatar_url: user.user_metadata?.avatar_url,
          subscription_plan: 'free',
          subscription_status: 'active'
        });
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Failed to load user data:', error);
    }
  }, []);

  const loadSettings = useCallback(async () => {
    try {
      // Check if user is authenticated first
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('User not authenticated, skipping settings load');
        return;
      }

      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', user.id)
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      if (data) {
        setSettings(prev => ({ ...prev, ...data }));
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }, []);

  const loadStats = useCallback(async () => {
    try {
      // In a real app, this would aggregate data from various tables
      const mockStats: AppStats = {
        total_scrapes: 156,
        total_exports: 23,
        total_leads: 1247,
        total_keywords: 892,
        api_requests_used: 7834,
        api_requests_limit: 10000,
        storage_used: 245,
        storage_limit: 1000
      };
      setStats(mockStats);
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  }, []);

  const loadRecentActivity = useCallback(async () => {
    try {
      const mockActivity: RecentActivity[] = [
        {
          id: '1',
          type: 'scrape',
          title: 'Product Data Scrape',
          description: 'Scraped 150 products from e-commerce site',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          status: 'success'
        },
        {
          id: '2',
          type: 'export',
          title: 'Lead Export',
          description: 'Exported 89 leads to CSV',
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
          status: 'success'
        }
      ];
      setRecentActivity(mockActivity);
    } catch (error) {
      console.error('Failed to load recent activity:', error);
    }
  }, []);

  const loadNotifications = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('read', false)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) {
        // If table doesn't exist, just use empty array instead of throwing
        if (error.code === 'PGRST116' || error.message.includes('does not exist')) {
          console.warn('Notifications table does not exist, using empty array');
          setNotifications([]);
          return;
        }
        throw error;
      }
      setNotifications(data || []);
    } catch (error) {
      console.error('Failed to load notifications:', error);
      // Set empty array on any error to prevent app hanging
      setNotifications([]);
    }
  }, []);

  // Utility functions
  const toggleSidebar = useCallback(() => {
    setSidebarOpen(prev => !prev);
  }, []);

  const updateSettings = useCallback(async (newSettings: Partial<AppSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings };
      setSettings(updatedSettings);

      // Save to database
      await supabase
        .from('user_settings')
        .upsert([updatedSettings]);
    } catch (error) {
      console.error('Failed to update settings:', error);
    }
  }, [settings]);

  const refreshStats = useCallback(async () => {
    await loadStats();
  }, [loadStats]);

  const addActivity = useCallback((activity: Omit<RecentActivity, 'id' | 'timestamp'>) => {
    const newActivity: RecentActivity = {
      ...activity,
      id: Date.now().toString(),
      timestamp: new Date().toISOString()
    };

    setRecentActivity(prev => [newActivity, ...prev.slice(0, 9)]);
  }, []);

  const addNotification = useCallback(async (notification: any) => {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .insert([{
          ...notification,
          read: false,
          created_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;
      setNotifications(prev => [data, ...prev]);
    } catch (error) {
      console.error('Failed to add notification:', error);
    }
  }, []);

  const removeNotification = useCallback(async (id: string) => {
    try {
      await supabase
        .from('notifications')
        .delete()
        .eq('id', id);

      setNotifications(prev => prev.filter(n => n.id !== id));
    } catch (error) {
      console.error('Failed to remove notification:', error);
    }
  }, []);

  const markNotificationAsRead = useCallback(async (id: string) => {
    try {
      await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', id);

      setNotifications(prev => prev.map(n =>
        n.id === id ? { ...n, read: true } : n
      ));
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  }, []);

  const value: AppContextType = {
    // UI State
    sidebarOpen,
    toggleSidebar,
    currentTool,
    setCurrentTool,
    isLoading,
    setIsLoading,
    globalError,
    setGlobalError,

    // User & Auth
    user,
    setUser,
    isAuthenticated,

    // App Data
    scrapingData,
    setScrapingData,

    // Settings
    settings,
    updateSettings,

    // Statistics
    stats,
    refreshStats,

    // Recent Activity
    recentActivity,
    addActivity,

    // Notifications
    notifications,
    addNotification,
    removeNotification,
    markNotificationAsRead
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};