import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle } from 'lucide-react';
import ResetPasswordForm from '@/components/auth/ResetPasswordForm';
import { supabase } from '@/lib/supabase';

const ResetPassword: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    const handlePasswordReset = async () => {
      try {
        // Check URL parameters first
        let accessToken = searchParams.get('access_token');
        let refreshToken = searchParams.get('refresh_token');
        let type = searchParams.get('type');

        // If not found in search params, check URL hash (Supabase sometimes uses fragments)
        if (!accessToken || !refreshToken) {
          const hash = window.location.hash.substring(1);
          const hashParams = new URLSearchParams(hash);
          accessToken = accessToken || hashParams.get('access_token');
          refreshToken = refreshToken || hashParams.get('refresh_token');
          type = type || hashParams.get('type');
        }

        console.log('Reset password tokens:', { accessToken: !!accessToken, refreshToken: !!refreshToken, type });

        if (type !== 'recovery' || !accessToken || !refreshToken) {
          setError('Invalid or expired password reset link. Please request a new one.');
          setLoading(false);
          return;
        }

        // Set the session with the tokens from the URL
        const { error } = await supabase.auth.setSession({
          access_token: accessToken,
          refresh_token: refreshToken
        });

        if (error) {
          console.error('Session error:', error);
          setError('Invalid or expired password reset link. Please request a new one.');
        }
      } catch (err) {
        console.error('Reset password error:', err);
        setError('An error occurred while processing your request.');
      } finally {
        setLoading(false);
      }
    };

    handlePasswordReset();
  }, [searchParams]);

  const handleSuccess = () => {
    setSuccess(true);
    // Redirect to login after a delay
    setTimeout(() => {
      navigate('/', { replace: true });
    }, 3000);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Verifying reset link...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <XCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl">Reset Link Invalid</CardTitle>
            <CardDescription>
              The password reset link is invalid or has expired
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4 border-red-200 bg-red-50">
              <AlertDescription className="text-red-700">{error}</AlertDescription>
            </Alert>
            <div className="text-center">
              <button
                onClick={() => navigate('/', { replace: true })}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Return to Sign In
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-xl">Password Updated</CardTitle>
            <CardDescription>
              Your password has been successfully updated
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4 border-green-200 bg-green-50">
              <AlertDescription className="text-green-700">
                You can now sign in with your new password. Redirecting you to the sign in page...
              </AlertDescription>
            </Alert>
            <div className="text-center">
              <button
                onClick={() => navigate('/', { replace: true })}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Sign In Now
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
      <ResetPasswordForm onSuccess={handleSuccess} onError={handleError} />
    </div>
  );
};

export default ResetPassword;
