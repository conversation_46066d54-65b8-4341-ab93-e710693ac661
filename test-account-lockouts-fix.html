<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Lockouts 406 Error Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
        .status-loading { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Account Lockouts 406 Error Fix Test</h1>
        <p>This tool tests the account_lockouts table queries to verify the 406 error has been resolved.</p>
        
        <div class="test-section">
            <h3>📋 Instructions</h3>
            <ol>
                <li>First, run the SQL script: <code>database/fix_account_lockouts_406.sql</code> in your Supabase SQL Editor</li>
                <li>Then click the test buttons below to verify the fix</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔍 Test 1: Basic Table Query</h3>
            <p>Tests if we can query the account_lockouts table without errors</p>
            <button onclick="testBasicQuery()">Test Basic Query</button>
            <div id="basicQueryResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔒 Test 2: Account Lockout Check</h3>
            <p>Tests the specific query that was causing the 406 error</p>
            <input type="email" id="testEmail" placeholder="Enter email to test (e.g., <EMAIL>)" value="<EMAIL>" style="width: 300px; padding: 8px; margin-right: 10px;">
            <button onclick="testAccountLockoutCheck()">Test Lockout Check</button>
            <div id="lockoutCheckResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Test 3: Create and Query Test Lockout</h3>
            <p>Creates a test lockout record and then queries it</p>
            <button onclick="testCreateAndQuery()">Test Create & Query</button>
            <div id="createQueryResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test 4: Table Structure Verification</h3>
            <p>Verifies the table structure and permissions</p>
            <button onclick="testTableStructure()">Test Table Structure</button>
            <div id="structureResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script type="module">
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        // Initialize Supabase client with enhanced headers
        const supabaseUrl = 'https://rclikclltlyzyojjttqv.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzOTYzOTIsImV4cCI6MjA2NTk3MjM5Mn0.Ktdh-7B_tZIbNJQqsrhFLun-CqL47mwLTTN9sUm4wKw';
        
        const supabase = createClient(supabaseUrl, supabaseKey, {
            auth: {
                autoRefreshToken: true,
                persistSession: true,
                detectSessionInUrl: true
            },
            global: {
                headers: {
                    'Accept': 'application/json, application/vnd.pgrst.object+json',
                    'Content-Type': 'application/json',
                    'Prefer': 'return=representation'
                },
            },
            db: {
                schema: 'public'
            }
        });

        // Helper function to show result
        function showResult(elementId, message, type = 'result') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // Test 1: Basic table query
        window.testBasicQuery = async function() {
            try {
                showResult('basicQueryResult', 'Testing basic query...', 'loading');
                
                const { data, error } = await supabase
                    .from('account_lockouts')
                    .select('*')
                    .limit(5);
                
                if (error) {
                    showResult('basicQueryResult', `❌ Error: ${error.message}\nCode: ${error.code}\nDetails: ${error.details}`, 'error');
                } else {
                    showResult('basicQueryResult', `✅ Success! Query returned ${data.length} records.\n\nData: ${JSON.stringify(data, null, 2)}`, 'success');
                }
            } catch (err) {
                showResult('basicQueryResult', `❌ Exception: ${err.message}`, 'error');
            }
        };

        // Test 2: Account lockout check (the original failing query)
        window.testAccountLockoutCheck = async function() {
            try {
                const email = document.getElementById('testEmail').value;
                if (!email) {
                    showResult('lockoutCheckResult', '❌ Please enter an email address', 'error');
                    return;
                }

                showResult('lockoutCheckResult', `Testing lockout check for ${email}...`, 'loading');
                
                // This is the exact query that was failing with 406 error
                const { data, error } = await supabase
                    .from('account_lockouts')
                    .select('*')
                    .eq('email', email)
                    .gte('locked_until', new Date().toISOString());
                
                if (error) {
                    showResult('lockoutCheckResult', `❌ Error: ${error.message}\nCode: ${error.code}\nDetails: ${error.details}`, 'error');
                } else {
                    const isLocked = data && data.length > 0;
                    showResult('lockoutCheckResult', `✅ Success! Account lockout check completed.\n\nEmail: ${email}\nIs Locked: ${isLocked}\nActive Lockouts: ${data.length}\n\nData: ${JSON.stringify(data, null, 2)}`, 'success');
                }
            } catch (err) {
                showResult('lockoutCheckResult', `❌ Exception: ${err.message}`, 'error');
            }
        };

        // Test 3: Create and query test lockout
        window.testCreateAndQuery = async function() {
            try {
                showResult('createQueryResult', 'Creating test lockout record...', 'loading');
                
                const testEmail = `test-${Date.now()}@example.com`;
                const lockedUntil = new Date();
                lockedUntil.setMinutes(lockedUntil.getMinutes() + 30); // Lock for 30 minutes
                
                // Create test lockout
                const { data: insertData, error: insertError } = await supabase
                    .from('account_lockouts')
                    .insert({
                        email: testEmail,
                        locked_until: lockedUntil.toISOString(),
                        attempt_count: 3,
                        reason: 'Test lockout for 406 error verification'
                    })
                    .select();
                
                if (insertError) {
                    showResult('createQueryResult', `❌ Insert Error: ${insertError.message}`, 'error');
                    return;
                }
                
                // Query it back
                const { data: queryData, error: queryError } = await supabase
                    .from('account_lockouts')
                    .select('*')
                    .eq('email', testEmail);
                
                if (queryError) {
                    showResult('createQueryResult', `❌ Query Error: ${queryError.message}`, 'error');
                    return;
                }
                
                // Clean up
                await supabase
                    .from('account_lockouts')
                    .delete()
                    .eq('email', testEmail);
                
                showResult('createQueryResult', `✅ Success! Created and queried test lockout record.\n\nInserted: ${JSON.stringify(insertData, null, 2)}\n\nQueried: ${JSON.stringify(queryData, null, 2)}\n\nTest record cleaned up.`, 'success');
                
            } catch (err) {
                showResult('createQueryResult', `❌ Exception: ${err.message}`, 'error');
            }
        };

        // Test 4: Table structure verification
        window.testTableStructure = async function() {
            try {
                showResult('structureResult', 'Checking table structure...', 'loading');
                
                // Try to get table info by querying with limit 0
                const { data, error } = await supabase
                    .from('account_lockouts')
                    .select('*')
                    .limit(0);
                
                if (error) {
                    showResult('structureResult', `❌ Table access error: ${error.message}`, 'error');
                } else {
                    showResult('structureResult', `✅ Table structure verification successful!\n\nTable exists and is accessible.\nQuery executed without errors.\nRLS policies are working correctly.`, 'success');
                }
            } catch (err) {
                showResult('structureResult', `❌ Exception: ${err.message}`, 'error');
            }
        };
    </script>
</body>
</html>
