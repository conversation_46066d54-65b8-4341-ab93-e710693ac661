// Test database connectivity and create missing tables if needed
import { createClient } from '@supabase/supabase-js';

const config = {
  supabase: {
    url: process.env.VITE_SUPABASE_URL || 'https://rclikclltlyzyojjttqv.supabase.co',
    anonKey: process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzOTYzOTIsImV4cCI6MjA2NTk3MjM5Mn0.Ktdh-7B_tZIbNJQqsrhFLun-CqL47mwLTTN9sUm4wKw'
  }
};

const supabase = createClient(config.supabase.url, config.supabase.anonKey);

async function testDatabase() {
  console.log('🔍 Testing database connectivity...');
  
  try {
    // Test basic connectivity
    const { data, error } = await supabase
      .from('keyword_searches')
      .select('*')
      .limit(1);
    
    if (error) {
      console.error('❌ Database connection failed:', error.message);
      return false;
    }
    
    console.log('✅ Database connection successful');
    return true;
  } catch (err) {
    console.error('❌ Database test failed:', err.message);
    return false;
  }
}

async function checkTables() {
  console.log('🔍 Checking required tables...');
  
  const requiredTables = [
    'keyword_searches',
    'export_jobs', 
    'export_data',
    'user_profiles',
    'scraping_jobs',
    'scraped_data'
  ];
  
  for (const table of requiredTables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ Table '${table}' missing or inaccessible:`, error.message);
      } else {
        console.log(`✅ Table '${table}' exists and accessible`);
      }
    } catch (err) {
      console.log(`❌ Error checking table '${table}':`, err.message);
    }
  }
}

async function testAuth() {
  console.log('🔍 Testing authentication...');
  
  try {
    const { data, error } = await supabase.auth.getSession();
    
    if (error) {
      console.log('❌ Auth test failed:', error.message);
    } else {
      console.log('✅ Auth system accessible');
      console.log('Current session:', data.session ? 'Active' : 'None');
    }
  } catch (err) {
    console.log('❌ Auth test error:', err.message);
  }
}

async function testPasswordReset() {
  console.log('🔍 Testing password reset functionality...');
  
  try {
    // Test with a dummy email (this will fail but we can see the error)
    const { error } = await supabase.auth.resetPasswordForEmail('<EMAIL>', {
      redirectTo: 'http://localhost:8080/reset-password'
    });
    
    if (error) {
      if (error.message.includes('redirect')) {
        console.log('❌ Redirect URL not configured in Supabase dashboard');
        console.log('   Please add http://localhost:8080/reset-password to your Supabase redirect URLs');
      } else {
        console.log('❌ Password reset test failed:', error.message);
      }
    } else {
      console.log('✅ Password reset functionality working');
    }
  } catch (err) {
    console.log('❌ Password reset test error:', err.message);
  }
}

async function main() {
  console.log('🚀 Starting database and auth tests...\n');
  
  const isConnected = await testDatabase();
  if (!isConnected) {
    console.log('\n❌ Cannot proceed - database connection failed');
    return;
  }
  
  console.log('');
  await checkTables();
  
  console.log('');
  await testAuth();
  
  console.log('');
  await testPasswordReset();
  
  console.log('\n✅ Tests completed!');
  console.log('\nNext steps:');
  console.log('1. If tables are missing, run the SQL scripts in your Supabase dashboard');
  console.log('2. If redirect URL error, add http://localhost:8080/reset-password to Supabase');
  console.log('3. Use the test tool at http://localhost:8080/test-password-reset.html');
}

main().catch(console.error);
