<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Password Reset</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        .info { background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .error { background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { background: #e6ffe6; padding: 15px; border-radius: 5px; margin: 10px 0; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #005a87; }
        input { padding: 10px; margin: 5px; border: 1px solid #ccc; border-radius: 5px; width: 300px; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Password Reset Test Tool</h1>
        
        <div class="info">
            <h3>Current URL Information:</h3>
            <pre id="urlInfo"></pre>
        </div>

        <div class="info">
            <h3>Test Password Reset Email</h3>
            <input type="email" id="emailInput" placeholder="Enter email address" value="<EMAIL>">
            <button onclick="testPasswordReset()">Send Reset Email</button>
        </div>

        <div id="result"></div>

        <div class="info">
            <h3>Supabase Configuration Check</h3>
            <button onclick="checkSupabaseConfig()">Check Configuration</button>
        </div>

        <div id="configResult"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Initialize Supabase
        const supabaseUrl = 'https://rclikclltlyzyojjttqv.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzOTYzOTIsImV4cCI6MjA2NTk3MjM5Mn0.Ktdh-7B_tZIbNJQqsrhFLun-CqL47mwLTTN9sUm4wKw';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        // Display current URL information
        function displayUrlInfo() {
            const urlInfo = {
                'Full URL': window.location.href,
                'Search Params': window.location.search,
                'Hash': window.location.hash,
                'Pathname': window.location.pathname
            };

            // Parse search params
            const searchParams = new URLSearchParams(window.location.search);
            const searchParamsObj = {};
            for (const [key, value] of searchParams) {
                searchParamsObj[key] = value;
            }

            // Parse hash params
            const hash = window.location.hash.substring(1);
            const hashParams = new URLSearchParams(hash);
            const hashParamsObj = {};
            for (const [key, value] of hashParams) {
                hashParamsObj[key] = value;
            }

            urlInfo['Parsed Search Params'] = searchParamsObj;
            urlInfo['Parsed Hash Params'] = hashParamsObj;

            document.getElementById('urlInfo').textContent = JSON.stringify(urlInfo, null, 2);
        }

        // Test password reset
        async function testPasswordReset() {
            const email = document.getElementById('emailInput').value;
            const resultDiv = document.getElementById('result');
            
            if (!email) {
                resultDiv.innerHTML = '<div class="error">Please enter an email address</div>';
                return;
            }

            resultDiv.innerHTML = '<div class="info">Sending password reset email...</div>';

            try {
                const { error } = await supabase.auth.resetPasswordForEmail(email, {
                    redirectTo: `${window.location.origin}/reset-password`
                });

                if (error) {
                    resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                } else {
                    resultDiv.innerHTML = `<div class="success">Password reset email sent to ${email}! Check your email and click the reset link.</div>`;
                }
            } catch (err) {
                resultDiv.innerHTML = `<div class="error">Unexpected error: ${err.message}</div>`;
            }
        }

        // Check Supabase configuration
        async function checkSupabaseConfig() {
            const configDiv = document.getElementById('configResult');
            configDiv.innerHTML = '<div class="info">Checking Supabase configuration...</div>';

            try {
                // Test basic connection
                const { data, error } = await supabase.auth.getSession();
                
                const config = {
                    'Supabase URL': supabaseUrl,
                    'Connection Test': error ? `Error: ${error.message}` : 'Success',
                    'Current Session': data.session ? 'Active session found' : 'No active session',
                    'Expected Redirect URL': `${window.location.origin}/reset-password`
                };

                configDiv.innerHTML = `<div class="info"><pre>${JSON.stringify(config, null, 2)}</pre></div>`;
            } catch (err) {
                configDiv.innerHTML = `<div class="error">Configuration check failed: ${err.message}</div>`;
            }
        }

        // Initialize on page load
        displayUrlInfo();
    </script>
</body>
</html>
